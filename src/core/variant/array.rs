use crate::core::variant::{Int32, Variant};

pub struct Array {
    pub value: Vec<Variant>,
}

pub struct TypedArray {
    pub value: Vec<Variant>,
    pub type_of: Box<Variant>,
}

impl Array {
    #[inline(always)]
    pub const fn new() -> Self {
        Self {
            value: Vec::new(),
        }
    }

    pub fn new_custom(size: usize) -> Self {
        Self {
            value: Vec::with_capacity(size),
        }
    }

    #[inline(always)]
    pub const fn from_array(value: Vec<Variant>) -> Self {
        Self { value }
    }

    pub const fn size(self) -> Int32 {
        Int32::new(self.value.len() as i32)
    }




}