pub mod aabb;
pub mod array;
pub mod basis;
pub mod bool;
pub mod byte;
pub mod callable;
pub mod color;
pub mod dictionary;
pub mod float;
pub mod int;
pub mod nodepath;
pub mod object;
pub mod packedarray;
pub mod plane;
pub mod projection;
pub mod quaternion;
pub mod rect2;
pub mod rect2i;
pub mod rid;
pub mod signal;
pub mod string;
pub mod stringname;
pub mod transform2d;
pub mod transform3d;
pub mod vector2;
pub mod vector2i;
pub mod vector3;
pub mod vector3i;
pub mod vector4;
pub mod vector4i;

pub use aabb::AABB;
pub use array::Array;
pub use array::TypedArray;
pub use basis::Basis;
pub use bool::Bool;
pub use byte::Byte;
pub use callable::Callable;
pub use color::Color;
pub use dictionary::Dictionary;
pub use float::Float32;
pub use float::Float64;
pub use int::Int32;
pub use int::Int64;
pub use nodepath::NodePath;
pub use object::Object;
pub use packedarray::PackedByteArray;
pub use packedarray::PackedColorArray;
pub use packedarray::PackedFloat32Array;
pub use packedarray::PackedFloat64Array;
pub use packedarray::PackedInt32Array;
pub use packedarray::PackedInt64Array;
pub use packedarray::PackedStringArray;
pub use packedarray::PackedVector2Array;
pub use packedarray::PackedVector2iArray;
pub use packedarray::PackedVector3Array;
pub use packedarray::PackedVector3iArray;
pub use packedarray::PackedVector4Array;
pub use packedarray::PackedVector4iArray;
pub use packedarray::PackedBoolArray;
pub use plane::Plane;
pub use projection::Projection;
pub use quaternion::Quaternion;
pub use rect2::Rect2;
pub use rect2i::Rect2i;
pub use rid::RID;
pub use signal::Signal;
pub use string::String;
pub use stringname::StringName;
pub use transform2d::Transform2D;
pub use transform3d::Transform3D;
pub use vector2::Vector2;
pub use vector2i::Vector2i;
pub use vector3::Vector3;
pub use vector3i::Vector3i;
pub use vector4::Vector4;
pub use vector4i::Vector4i;

pub enum Variant {
    AABB(AABB),
    Array(Array),
    TypedArray(TypedArray),
    Basis(Basis),
    Bool(Bool),
    Byte(Byte),
    Callable(Callable),
    Color(Color),
    Dictionary(Dictionary),
    Float32(Float32),
    Float64(Float64),
    Int32(Int32),
    Int64(Int64),
    NodePath(NodePath),
    Object(Object),
    PackedByteArray(PackedByteArray),
    PackedColorArray(PackedColorArray),
    PackedFloat32Array(PackedFloat32Array),
    PackedFloat64Array(PackedFloat64Array),
    PackedInt32Array(PackedInt32Array),
    PackedInt64Array(PackedInt64Array),
    PackedStringArray(PackedStringArray),
    PackedVector2Array(PackedVector2Array),
    PackedVector2iArray(PackedVector2iArray),
    PackedVector3Array(PackedVector3Array),
    PackedVector3iArray(PackedVector3iArray),
    PackedVector4Array(PackedVector4Array),
    PackedVector4iArray(PackedVector4iArray),
    PackedBoolArray(PackedBoolArray),
    Plane(Plane),
    Projection(Projection),
    Quaternion(Quaternion),
    Rect2(Rect2),
    Rect2i(Rect2i),
    RID(RID),
    Signal(Signal),
    String(String),
    StringName(StringName),
    Transform2D(Transform2D),
    Transform3D(Transform3D),
    Vector2(Vector2),
    Vector2i(Vector2i),
    Vector3(Vector3),
    Vector3i(Vector3i),
    Vector4(Vector4),
    Vector4i(Vector4i),
}