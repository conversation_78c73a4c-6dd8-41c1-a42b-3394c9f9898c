use crate::core::variant::{Byte, Float32};

pub struct Color {
    pub a: Float32,
    pub a8: Byte,
    pub b: Float32,
    pub b8: Byte,
    pub g: Float32,
    pub g8: Byte,
    pub h: Float32,
    pub ok_hsl_h: Float32,
    pub ok_hsl_l: Float32,
    pub ok_hsl_s: Float32,
    pub ok_hsv_h: Float32,
    pub ok_hsv_s: Float32,
    pub ok_hsv_v: Float32,
    pub r: Float32,
    pub r8: Byte,
    pub s: Float32,
    pub v: Float32,
}
