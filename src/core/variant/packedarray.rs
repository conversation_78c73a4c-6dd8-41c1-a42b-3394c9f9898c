use crate::core::variant::{Bool, Byte, Color, Float32, Float64, Int32, Int64, String, Vector2, Vector2i, Vector3, Vector3i, Vector4, Vector4i};

pub struct PackedByteArray {
    value: Vec<Byte>,
}

pub struct PackedColorArray {
    value: Vec<Color>,
}

pub struct PackedFloat32Array {
    value: Vec<Float32>,
}

pub struct PackedFloat64Array {
    value: Vec <Float64>,
}

pub struct PackedInt32Array {
    value: Vec<Int32>,
}

pub struct PackedInt64Array {
    value: Vec<Int64>,
}

pub struct PackedStringArray {
    value: Vec<String>,
}

pub struct PackedVector2Array {
    value: Vec<Vector2>,
}

pub struct PackedVector2iArray {
    value: Vec<Vector2i>,
}

pub struct PackedVector3Array {
    value: Vec<Vector3>,
}

pub struct PackedVector3iArray {
    value: Vec<Vector3i>,
}

pub struct PackedVector4Array {
    value: Vec<Vector4>,
}

pub struct PackedVector4iArray {
    value: Vec<Vector4i>,
}

pub struct PackedBoolArray {
    value: Vec<Bool>,
}
